﻿  Assembling include\syscalls_masm.asm...
  Dllmain.cpp
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(21,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(22,1): warning C4005: “STATUS_PENDING”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(738,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(530,30): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(613,26): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(920,87): warning C4267: “参数”: 从“size_t”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(936,45): error C2672: “nullgate::syscalls::SCall”: 未找到匹配的重载函数
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(147,12):
  可能是“NTSTATUS nullgate::syscalls::SCall(uint64_t,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(936,45):
  	未满足关联约束
  		C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(146,14):
  		计算结果为 false 的概念“std::invocable<CusNtWriteVirtualMemory,HANDLE&,LPVOID&,const nullgate::obfuscation::DataUnit*&&,std::array<nullgate::obfuscation::DataUnit,389>::size_type&&,SIZE_T*&&>”
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\concepts(262,16):
  			“invoke”: 未找到匹配的重载函数
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1735,19):
  				可能是“unknown-type std::invoke(_Callable &&,_Ty1 &&,_Types2 &&...) noexcept(<expr>)”
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1729,19):
  				或    “unknown-type std::invoke(_Callable &&) noexcept(<expr>)”
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(124,12):
  或    “NTSTATUS nullgate::syscalls::SCall(const std::string &,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  	“初始化”: 无法从“uint64_t”转换为“const std::string &”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  		原因如下: 无法从“uint64_t”转换为“const std::string”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  		“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string”: 没有重载函数可以转换所有参数类型
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2558,5):
  			可能是“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”: 无法将参数 1 从“uint64_t”转换为“std::nullptr_t”
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  					仅空指针常数可转换为 nullptr_t
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2545,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”
          with
          [
              _Elem=char
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”: 无法将参数 1 从“uint64_t”转换为“const _Elem *const ”
          with
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  					从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(3092,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”: 无法将参数 1 从“uint64_t”转换为“std::initializer_list<_Elem>”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  					转换要求第二个用户定义的转换运算符或构造函数
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2909,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Ty &,const unsigned __int64,const unsigned __int64,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2763,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::from_range_t,_Rng &&,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2575,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(_Iter,_Iter,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2569,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const unsigned __int64,const _Elem,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2552,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const ,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(937,47):
  			尝试匹配参数列表“(uint64_t)”时
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(954,36): error C2672: “nullgate::syscalls::SCall”: 未找到匹配的重载函数
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(147,12):
  可能是“NTSTATUS nullgate::syscalls::SCall(uint64_t,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(954,36):
  	未满足关联约束
  		C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(146,14):
  		计算结果为 false 的概念“std::invocable<CusNtWriteVirtualMemory,HANDLE&,LPVOID&,const nullgate::obfuscation::DataUnit*&&,std::array<nullgate::obfuscation::DataUnit,389>::size_type&&,SIZE_T*&&>”
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\concepts(262,16):
  			“invoke”: 未找到匹配的重载函数
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1735,19):
  				可能是“unknown-type std::invoke(_Callable &&,_Ty1 &&,_Types2 &&...) noexcept(<expr>)”
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1729,19):
  				或    “unknown-type std::invoke(_Callable &&) noexcept(<expr>)”
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(124,12):
  或    “NTSTATUS nullgate::syscalls::SCall(const std::string &,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  	“初始化”: 无法从“uint64_t”转换为“const std::string &”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  		原因如下: 无法从“uint64_t”转换为“const std::string”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  		“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string”: 没有重载函数可以转换所有参数类型
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2558,5):
  			可能是“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”: 无法将参数 1 从“uint64_t”转换为“std::nullptr_t”
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  					仅空指针常数可转换为 nullptr_t
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2545,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”
          with
          [
              _Elem=char
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”: 无法将参数 1 从“uint64_t”转换为“const _Elem *const ”
          with
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  					从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(3092,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”: 无法将参数 1 从“uint64_t”转换为“std::initializer_list<_Elem>”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  					转换要求第二个用户定义的转换运算符或构造函数
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2909,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Ty &,const unsigned __int64,const unsigned __int64,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2763,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::from_range_t,_Rng &&,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2575,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(_Iter,_Iter,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2569,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const unsigned __int64,const _Elem,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2552,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const ,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(955,47):
  			尝试匹配参数列表“(uint64_t)”时
  
  obfuscation.cpp
  syscalls.cpp
